# Logged-In CTA Testing Guide

## Overview
This guide provides manual testing steps to verify that the logged-in CTA functionality is working correctly across all three CTA locations: Header CTAs, Scroll Nav CTAs, and Mobile Scroll Nav CTAs.

## Implementation Summary

### Changes Made:
1. **Added 12 new CTA constants** in `Ctas_Meta.php` for logged-in versions of each CTA type
2. **Created 12 new field methods** with 50% width styling for side-by-side display
3. **Updated existing fields** to 50% width to accommodate the new logged-in fields
4. **Modified CTA logic** in `With_Navigation_Scroll_CTA` trait and `Masthead_Controller` to check user login status
5. **Added fallback logic** to use default CTAs when logged-in CTAs are empty

### Files Modified:
- `wp-content/plugins/core/src/Object_Meta/Ctas_Meta.php`
- `wp-content/plugins/core/src/Templates/Components/Traits/With_Navigation_Scroll_CTA.php`
- `wp-content/themes/core/components/header/masthead/Masthead_Controller.php`

## Manual Testing Steps

### 1. Admin Configuration Test
1. Log in to WordPress admin
2. Navigate to **Theme Settings > CTAs Options**
3. Verify that each CTA section now shows two fields side-by-side:
   - Left field: Default CTA (50% width)
   - Right field: Logged In CTA (50% width)
4. Configure different URLs/labels for default and logged-in CTAs for each section:
   - Articles/Podcasts Scroll Nav CTA
   - Listicles Scroll Nav CTA
   - Services Listicles Scroll Nav CTA
   - Pages Scroll Nav CTA
   - Articles/Podcasts Mobile Scroll Nav CTA
   - Listicles Mobile Scroll Nav CTA
   - Services Listicles Mobile Scroll Nav CTA
   - Pages Mobile Scroll Nav CTA
   - Articles/Podcasts Header CTA
   - Listicles Header CTA
   - Services Listicles Header CTA
   - Pages Header CTA

### 2. Frontend Testing - Not Logged In
1. **Log out** of WordPress (or use incognito/private browsing)
2. Visit different post types:
   - Regular Post/Article
   - Tool Post (Listicle)
   - Service Post
   - Regular Page
3. For each post type, verify:
   - **Header CTA**: Shows the default CTA configured in admin
   - **Scroll Nav CTA**: Shows the default scroll nav CTA when scrolling
   - **Mobile Scroll Nav CTA**: Shows the default mobile CTA on mobile devices

### 3. Frontend Testing - Logged In
1. **Log in** to WordPress
2. Visit the same post types as above
3. For each post type, verify:
   - **Header CTA**: Shows the logged-in CTA if configured, otherwise falls back to default
   - **Scroll Nav CTA**: Shows the logged-in scroll nav CTA if configured, otherwise falls back to default
   - **Mobile Scroll Nav CTA**: Shows the logged-in mobile CTA if configured, otherwise falls back to default

### 4. Fallback Testing
1. Configure only default CTAs (leave logged-in CTAs empty)
2. Test as logged-in user
3. Verify that default CTAs are shown (fallback working correctly)

### 5. Post Type Specific Testing
Test each post type to ensure correct CTA mapping:
- **Media/Post**: Uses `ARTICLES_PODCASTS_*` CTAs
- **Tool Post**: Uses `LISTICLES_*` CTAs  
- **Service Post**: Uses `SERVICES_LISTICLES_*` CTAs
- **Other Pages**: Uses `PAGES_*` CTAs

## Expected Behavior

### When User is NOT Logged In:
- All CTAs should display the default versions configured in the admin

### When User IS Logged In:
- If logged-in CTA is configured: Display the logged-in version
- If logged-in CTA is empty: Fall back to the default version
- This applies to all three CTA locations (Header, Scroll Nav, Mobile Scroll Nav)

## Troubleshooting

### CTAs Not Showing Different Content:
1. Verify user login status using `is_user_logged_in()`
2. Check that logged-in CTAs are properly configured in admin
3. Ensure ACF fields are saving correctly
4. Clear any caching (object cache, page cache, etc.)

### Admin Fields Not Displaying Correctly:
1. Verify ACF is active and updated
2. Check that field groups are properly registered
3. Ensure 50% width styling is applied to both default and logged-in fields

### Fallback Not Working:
1. Verify the fallback logic in the trait and controller methods
2. Check that default CTAs are configured
3. Test with empty logged-in CTA fields

## Code Verification Points

### Constants Added:
- `ARTICLES_PODCASTS_NAV_LINK_LOGGED_IN`
- `LISTICLES_NAV_LINK_LOGGED_IN`
- `SERVICES_LISTICLES_NAV_LINK_LOGGED_IN`
- `PAGES_NAV_LINK_LOGGED_IN`
- `ARTICLES_PODCASTS_NAV_MOBILE_LINK_LOGGED_IN`
- `LISTICLES_NAV_MOBILE_LINK_LOGGED_IN`
- `SERVICES_LISTICLES_NAV_MOBILE_LINK_LOGGED_IN`
- `PAGES_NAV_MOBILE_LINK_LOGGED_IN`
- `ARTICLES_PODCASTS_HEADER_CTA_LOGGED_IN`
- `LISTICLES_HEADER_CTA_LOGGED_IN`
- `SERVICES_LISTICLES_HEADER_CTA_LOGGED_IN`
- `PAGES_HEADER_CTA_LOGGED_IN`

### Key Methods Modified:
- `Ctas_Meta::get_keys()` - includes all new logged-in constants
- `Ctas_Meta::get_group_config()` - adds all logged-in fields with 50% width
- `With_Navigation_Scroll_CTA::get_default_scroll_nav_link()` - checks login status
- `With_Navigation_Scroll_CTA::get_default_scroll_nav_mobile_link()` - checks login status  
- `Masthead_Controller::get_default_header_cta_link()` - checks login status

## Success Criteria
✅ Admin shows 24 total CTA fields (12 default + 12 logged-in) arranged side-by-side
✅ Non-logged-in users see default CTAs
✅ Logged-in users see logged-in CTAs when configured
✅ Fallback to default CTAs works when logged-in CTAs are empty
✅ All three CTA locations (Header, Scroll Nav, Mobile) respect login status
✅ Post type specific CTA mapping works correctly
