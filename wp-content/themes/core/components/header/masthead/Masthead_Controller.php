<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\header\masthead;

use Tribe\Project\Integrations\Memberpress\Memberpress_Meta;
use Tribe\Project\Object_Meta\Post_Template_Editorial_Meta;
use Tribe\Project\Post_Types\Post\Post_Template_Editorial;
use Tribe\Project\Post_Types\Service_Post\Service_Post;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\Traits\With_Navigation_Scroll_CTA;
use Tribe\Project\Theme_Customizer\Customizer_Sections\Header;
use Tribe\Project\Theme\Branding\Customizer_Settings;
use Tribe\Project\Object_Meta\Ctas_Meta;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Post_Types\Tool_Post\Tool_Post;
use Tribe\Project\Post_Types\Post\Post;
use Tribe\Project\Post_Types\Media\Media;

class Masthead_Controller extends Abstract_Controller {
	use Memberpress_Meta;
	use With_Navigation_Scroll_CTA;

	private function get_home_url(): string {
		if ( function_exists( 'pll_home_url' ) ) {
			return trailingslashit( pll_home_url() );
		} else {
			return trailingslashit( home_url() );
		}
	}

	public function get_logo(): string {
		$header_tagline = get_theme_mod( Customizer_Settings::HEADER_TAGLINE, '' );

		if ( wp_is_mobile() && get_theme_mod( 'site_branding_nav_logo_mobile' ) ) {
			return sprintf(
				'<div class="logo" data-js="logo"><a href="%s" rel="home">%s<span class="screen-reader-text">%s</span></a>%s</div>',
				esc_url( $this->get_home_url() ),
				wp_get_attachment_image(
					get_theme_mod( 'site_branding_nav_logo_mobile' ),
					'medium',
					false,
					[ 'loading' => false ]
				),
				esc_html( get_bloginfo( 'name' ) ),
				isset( $header_tagline ) ? sprintf(
					'<span class="site-header__header-tagline">%s</span>',
					esc_html( $header_tagline ),
				) : '',
			);
		}

		return sprintf(
			'<div class="logo" data-js="logo"><a href="%s" rel="home">%s<span class="screen-reader-text">%s</span></a>%s</div>',
			esc_url( $this->get_home_url() ),
			wp_get_attachment_image(
				get_theme_mod( 'site_branding_nav_logo' ),
				'medium',
				false,
				[ 'loading' => false ]
			),
			esc_html( get_bloginfo( 'name' ) ),
			isset( $header_tagline ) ? sprintf(
				'<span class="site-header__header-tagline">%s</span>',
				esc_html( $header_tagline ),
			) : '',
		);
	}

	public function get_header_layout(): string {
		$layout = get_theme_mod( Header::SITE_BRANDING_LOGO_LOCATION, Header::SITE_BRANDING_LOGO_LOCATION_LEFT );
		$class  = 'logo-left';

		if ( Header::SITE_BRANDING_LOGO_LOCATION_CENTER === $layout ) {
			$class = 'logo-centered';
		}

		return $class;
	}

	public function has_cta(): bool {
		return ! empty( get_theme_mod( Header::SITE_BRANDING_CTA_URL ) );
	}

	/**
	 * @param string $cta_url
	 * @param string $cta_label
	 *
	 * @return Deferred_Component
	 */
	public function get_cta( string $cta_url = '', string $cta_label = '' ): Deferred_Component {
		$cta = [
			'content' => ! empty( $cta_label ) ?
				esc_html__( $cta_label ) :
				esc_html__( get_theme_mod( Header::SITE_BRANDING_CTA_LABEL, '' ) ),
			'url'     => ! empty( $cta_url ) ?
				esc_url( $cta_url ) :
				esc_url( get_theme_mod( Header::SITE_BRANDING_CTA_URL, '' ) ),
			'target'  => get_theme_mod( Header::SITE_BRANDING_CTA_TARGET, '' ),
		];

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::ID      => 'site-header__cta-link',
			Link_Controller::URL     => $cta['url'],
			Link_Controller::CONTENT => $cta['content'] ?: $cta['url'],
			Link_Controller::TARGET  => $cta['target'],
			Link_Controller::CLASSES => [
				'site-header__cta-link',
				'a-btn-secondary',
			],
		] );
	}

	public function ppl_is_active(): bool {
		return (bool) get_field( PPL_Options_Meta::PPL_IS_ACTIVE, get_the_ID() ) ?? false;
	}

	/**
	 * Get avatar
	 *
	 * @return string
	 */
	public function get_avatar(): string {
		return get_avatar( get_current_user_id(), 80 );
	}

	/**
	 * Get Dashboard link arguments
	 *
	 * @return array
	 */
	public function get_dashboard_button(): array {
		return $this->get_dashboard_link_args();
	}

	/**
	 * Get Dashboard Icon
	 *
	 * @return Deferred_Component
	 */
	public function get_dashboard_icon(): Deferred_Component {
		$dashboard_link_args = $this->get_dashboard_link_args();

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::URL     => $dashboard_link_args[ Link_Controller::URL ],
			Link_Controller::CLASSES => [
				'site-header__dashboard-link-icon',
			],
		] );
	}

	/**
	 * Check if user is logged in or not
	 * Todo: Attach to Memberpress?
	 *
	 * @return bool
	 */
	public function is_logged_in(): bool {
		return is_user_logged_in();
	}

	public function hide_navigation(): ?bool {
		$post_id = get_the_ID();

		// Check if the template of the post is the editorial
		if ( ! $post_id || ! is_single() || Post_Template_Editorial::TEMPLATE_FILE !== get_page_template_slug( $post_id ) ) {
			return false;
		}

		$hide_nav = get_field( Post_Template_Editorial_Meta::HIDE_NAVIGATION, $post_id );

		return (bool) $hide_nav ?? false;
	}

	/*
	 * Check if the Memberpress plugin is active
	 * @return bool
	 */
	public function is_memberpress_active(): bool {
		return class_exists( 'MeprUtils' );
	}

	/**
	 * Get Default Header CTA Link
	 *
	 * @return Deferred_Component|null
	 */
	public function get_default_header_cta_link(): ?Deferred_Component {
		// Check if user is logged in to determine which CTA to use
		$is_logged_in = is_user_logged_in();

		if ( is_singular( Media::NAME ) || is_singular( Post::NAME ) ) {
			$header_cta_link = $is_logged_in
				? get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::ARTICLES_PODCASTS_HEADER_CTA_LOGGED_IN, 'option' )
				: get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::ARTICLES_PODCASTS_HEADER_CTA, 'option' );
		} elseif ( is_singular( Tool_Post::NAME ) ) {
			$header_cta_link = $is_logged_in
				? get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::LISTICLES_HEADER_CTA_LOGGED_IN, 'option' )
				: get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::LISTICLES_HEADER_CTA, 'option' );
		} elseif ( is_singular( Service_Post::NAME ) ) {
			$header_cta_link = $is_logged_in
				? get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::SERVICES_LISTICLES_HEADER_CTA_LOGGED_IN, 'option' )
				: get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::SERVICES_LISTICLES_HEADER_CTA, 'option' );
		} else {
			$header_cta_link = $is_logged_in
				? get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::PAGES_HEADER_CTA_LOGGED_IN, 'option' )
				: get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::PAGES_HEADER_CTA, 'option' );
		}

		// If logged-in CTA is empty, fallback to default CTA
		if ( $is_logged_in && ! $header_cta_link ) {
			if ( is_singular( Media::NAME ) || is_singular( Post::NAME ) ) {
				$header_cta_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::ARTICLES_PODCASTS_HEADER_CTA, 'option' );
			} elseif ( is_singular( Tool_Post::NAME ) ) {
				$header_cta_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::LISTICLES_HEADER_CTA, 'option' );
			} elseif ( is_singular( Service_Post::NAME ) ) {
				$header_cta_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::SERVICES_LISTICLES_HEADER_CTA, 'option' );
			} else {
				$header_cta_link = get_field( Ctas_Meta::NAME . '_' . Ctas_Meta::PAGES_HEADER_CTA, 'option' );
			}
		}

		if ( is_singular( Tool_Post::NAME ) && $this->ppl_is_active() ) {
			if ( $header_cta_link ) {
				$tool_header_nav_link  = $header_cta_link['url'];
				$tool_header_nav_label = $header_cta_link['title'];
			} else {
				$tool_header_nav_link  = esc_url( get_theme_mod( Header::SITE_BRANDING_CTA_URL, '' ) );
				$tool_header_nav_label = esc_html( get_theme_mod( Header::SITE_BRANDING_CTA_LABEL, '' ) );
			}
		}

		if ( ! $header_cta_link ) {
			if ( $this->has_cta() ) {
				return $this->get_cta();
			} else {
				return null;
			}
		}

		return defer_template_part( 'components/link/link', null, [
			Link_Controller::ID      => 'site-header__cta-link',
			Link_Controller::URL     => $header_cta_link['url'],
			Link_Controller::CONTENT => $header_cta_link['title'],
			Link_Controller::TARGET  => $header_cta_link['target'],
			Link_Controller::CLASSES => [
				'site-header__cta-link',
				'a-btn-secondary',
			],
		] );
	}
}
