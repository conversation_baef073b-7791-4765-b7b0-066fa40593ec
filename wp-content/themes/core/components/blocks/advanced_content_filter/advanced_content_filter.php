<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\blocks\advanced_content_filter\Advanced_Content_Filter_Block_Controller;

/**
 * @var array $args Arguments passed to the template
 */
// phpcs:ignore VariableAnalysis.CodeAnalysis.VariableAnalysis.UndefinedVariable
$c = Advanced_Content_Filter_Block_Controller::factory( $args );

if ( ! $c->get_query_post_types() || ! $c->get_query_taxonomies() ) {
	if ( $error = $c->get_no_settings_error() ) {
		echo $error;
	}

	return;
}

?>
<section <?php echo $c->get_classes(); ?> <?php echo $c->get_attrs(); ?>>
	<div <?php echo $c->get_container_classes();?>>
		<div class="open-filter">
			<img src="<?php echo esc_url( get_theme_file_uri( 'assets/img/icons/filter.svg' ) ); ?>" alt="Open Filter Icon" />
		</div>

		<div class="b-advanced-content-filter__filter">
			<div class="b-advanced-content-filter__filter-container" data-js="content-filter">
				<div class="b-advanced-content-filter__heading">
					<span class="close-filter"></span>
					<?php echo $c->get_filter_title_with_tooltip(); ?>
				</div>
				<div class="b-advanced-content-filter__tags"></div>

				<div class="b-advanced-content-filter__filters">
					<?php echo $c->get_filters(); ?>
				</div>

				<div class="b-advanced-content-filter__ctas">
					<button class="a-btn-secondary <?php echo $c->get_reset_filters_trigger_class(); ?>" id="reset-filter">Reset</button>
					<button class="a-btn <?php echo $c->get_apply_filters_trigger_class(); ?>" id="apply-filter">Apply</button>
				</div>
			</div>
		</div>
		<div class="b-advanced-content-filter__content">
			<?php get_template_part(
				'components/content_block/content_block',
				null,
				$c->get_header_args()
			); ?>

			<?php 

			$c->render_posts( [ 'b-advanced-content-filter__content-container' ], $c->get_archive_post_loader_attrs() ); 
			
			?>

			<?php if ( $c->has_more_posts() ) { ?>
				<div class="b-advanced-content-filter__load-more-container">
					<?php
					$c->render_load_more_button( __( 'Load More', 'tribe' ), [
						'c-block__cta-link',
						'a-cta',
						'a-cta--secondary',
					], '', true );
					?>
				</div>
				<?php
			} ?>
		</div>
	</div>
</section>
