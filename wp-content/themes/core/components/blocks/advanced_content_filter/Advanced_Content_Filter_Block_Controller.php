<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\advanced_content_filter;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Admin\Editor\Blocks_Utilities;
use Tribe\Project\Components\Post_Loader\Post_Loader;
use Tribe\Project\Object_Meta\Theme_Settings;
use Tribe\Project\Templates\Components\card\Card_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\content_block\Content_Block_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\Post_Loader_Controller;
use Tribe\Project\Templates\Components\Traits\Categories_Utilities;
use Tribe\Project\Theme\Config\Image_Sizes;
use WP_Query;
use WP_Error;

class Advanced_Content_Filter_Block_Controller extends Post_Loader_Controller {
	use Categories_Utilities;

	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const TITLE             = 'title';
	public const HEADING_TAG       = 'heading_tag';
	public const ARCHIVE_TAXONOMY  = 'archive_taxonomy';
	public const ARCHIVE_TERM      = 'archive_term';
	public const QUERY_POST_TYPES  = 'query_post_types';
	public const QUERY_TAXONOMIES  = 'query_taxonomies';
	public const WORDS_LIMIT       = 12;
	public const POSTS_PER_PAGE    = 9;

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $title;
	private string $heading_tag;
	private array  $query_post_types;
	private array  $query_taxonomies;
	private string $archive_taxonomy;
	private string $archive_term;
	private string $unique_id;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->title             = (string) $args[ self::TITLE ];
		$this->heading_tag       = (string) $args[ self::HEADING_TAG ];
		$this->query_post_types  = (array) $args[ self::QUERY_POST_TYPES ];
		$this->query_taxonomies  = (array) $args[ self::QUERY_TAXONOMIES ];
		$this->archive_taxonomy  = (string) $args[ self::ARCHIVE_TAXONOMY ] ?: '';
		$this->archive_term      = (string) $args[ self::ARCHIVE_TERM ] ?: '';
		$this->unique_id         = uniqid();

		$this->render_ajax_posts();
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::TITLE             => '',
			self::HEADING_TAG       => '',
			self::QUERY_POST_TYPES  => [],
			self::QUERY_TAXONOMIES  => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [ 'l-container', 'b-advanced-content-filter__container' ],
			self::CLASSES           => [ 'c-block', 'b-advanced-content-filter' ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @return array
	 */
	public function get_header_args(): array {
		if ( empty( $this->title ) ) {
			return [];
		}

		return [
			Content_Block_Controller::TAG     => 'header',
			Content_Block_Controller::TITLE   => $this->get_title(),
			Content_Block_Controller::CLASSES => [
				'c-block__content-block',
				'c-block__header',
			],
		];
	}

	/**
	 * @return Deferred_Component
	 */
	private function get_title(): Deferred_Component {
		$title_classes = [ 'c-block__title', 'h3' ];

		if ( 'h1' === $this->heading_tag ) {
			$title_classes[] = 'page-title';
		}

		return defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => $this->heading_tag ?: 'h2',
			Text_Controller::CLASSES => $title_classes,
			Text_Controller::CONTENT => $this->title ?? '',
		] );
	}

	public function get_filter_title_with_tooltip(): Deferred_Component {
		$title = defer_template_part( 'components/text/text', null, [
			Text_Controller::TAG     => 'h6',
			Text_Controller::CLASSES => [ 'b-advanced-content-filter__filter-title' ],
			Text_Controller::CONTENT => __( 'Filter', 'tribe' ),
		] );

		$tooltip        = defer_template_part( 'components/tooltip/tooltip', null, [
			'attrs'   => [
				'itemprop' => 'filterComponentExplanation',
			],
			'content' => __( 'Select up to 5 options', 'tribe' ),
		] );
		$filter_tooltip = '<div class="b-advanced-content-filter__tooltip" data-js="tooltip-trigger"><span class="b-advanced-content-filter__tooltip-icon"></span>' . $tooltip . '</div>';

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [
				't-sink',
				's-sink',
				'b-advanced-content-filter__filter-tooltip',
			],
			Container_Controller::CONTENT => $title . $filter_tooltip,
		] );
	}

	public function get_terms_with_existing_posts( string $taxonomy_name ): array|null {
		global $wpdb;

		// Get the current language term IDs 
		$term_ids = get_terms( [
			'taxonomy'   => $taxonomy_name,
			'hide_empty' => false,
			'fields'     => 'ids',
		] );

		if ( empty( $term_ids ) ) {
			return null;
		}

		$term_placeholder       = implode( ',', array_fill( 0, count( $term_ids ), '%d' ) );
		$post_types_placeholder = implode( ',', array_fill( 0, count( $this->query_post_types ), '%s' ) );

		$query = [];

		$query[] = "SELECT DISTINCT t.term_id, t.name, t.slug FROM {$wpdb->posts} p";
		$query[] = "INNER JOIN {$wpdb->term_relationships} tr ON (p.ID = tr.object_id)";

		// If archive taxonomy and term are set, join them to filter results
		if ( $this->archive_taxonomy && $this->archive_term ) {
			$query[] = "INNER JOIN {$wpdb->term_relationships} tr2 ON (p.ID = tr2.object_id)";
			$query[] = "INNER JOIN {$wpdb->term_taxonomy} tt2 ON (tr2.term_taxonomy_id = tt2.term_taxonomy_id) AND tt2.taxonomy = '" . esc_sql( $this->archive_taxonomy ) . "'";
			$query[] = "INNER JOIN {$wpdb->terms} t2 ON (tt2.term_id = t2.term_id) AND t2.slug = '" . esc_sql( $this->archive_term ) . "'";
		}

		$query[] = "INNER JOIN {$wpdb->term_taxonomy} tt ON (tr.term_taxonomy_id = tt.term_taxonomy_id)";
		$query[] = "INNER JOIN {$wpdb->terms} t ON (tt.term_id = t.term_id)";
		$query[] = "WHERE p.post_status = 'publish'";
		$query[] = "AND p.post_type IN ($post_types_placeholder)";
		$query[] = "AND t.term_id IN ($term_placeholder)";
		$query[] = "AND tt.taxonomy = %s";

		$query = $wpdb->prepare(
			implode( ' ', $query ),
			array_merge( $this->query_post_types, $term_ids, [ $taxonomy_name ] ),
		);

		$results = $wpdb->get_results( $query );

		return $results ?: null;
	}

	public function get_filter_options( $taxonomy_name ): ?Deferred_Component {
		if ( ! $terms = $this->get_terms_with_existing_posts( $taxonomy_name ) ) {
			return null;
		}

		$options = '';

		foreach ( $terms as $term ) {
			$checkbox_input = '<input type="checkbox" class="b-advanced-content-filter__selector__checkbox" id="' . esc_attr( $term->slug ) . '-' . esc_attr( $this->unique_id ) . '" data-label="' . esc_attr( $term->name ) . '" name="' . esc_attr( $term->slug ) . '" value="' . esc_attr( $term->slug ) . '" />';
			$checkbox_label = defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $checkbox_input . $term->name,
				Container_Controller::TAG     => 'label',
				Container_Controller::CLASSES => [ 'b-advanced-content-filter__selector__label' ],
				Container_Controller::ATTRS   => [
					'for' => $term->slug . '-' . $this->unique_id,
				],
			] );
			$options        .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $checkbox_label,
				Container_Controller::TAG     => 'li',
				Container_Controller::CLASSES => [ 'b-advanced-content-filter__selector__option' ],
			] );
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CONTENT => $options,
			Container_Controller::TAG     => 'ul',
			Container_Controller::CLASSES => [ 'b-advanced-content-filter__selector__list' ],
		] );
	}

	public function query_posts( array $request_params ): WP_Query|WP_Error {
		$paged = (int) $request_params[ Post_Loader::PAGE_FIELD_NAME ] ?: 1;

		$args = [
			'post_type'           => $this->query_post_types,
			'posts_per_page'      => self::POSTS_PER_PAGE,
			'ignore_sticky_posts' => 1,
			'paged'               => $paged,
			'post_status'         => 'publish',
		];

		$tax_query = [];

		if ( ! empty( $request_params[ Post_Loader::TAXONOMY_FIELD_NAME ] ) && ! empty( $request_params[ Post_Loader::TERM_FIELD_NAME ] ) ) {
			$tax_query[] = [
				'taxonomy' => $request_params[ Post_Loader::TAXONOMY_FIELD_NAME ],
				'field'    => 'slug',
				'terms'    => $request_params[ Post_Loader::TERM_FIELD_NAME ],
			];
		}

		if ( $filters = $request_params[ Post_Loader::FILTERS_FIELD_NAME ] ) {
			foreach ( $filters as $taxonomy => $slugs ) {
				if ( ! is_string( $taxonomy ) || ! is_array( $slugs ) ) {
					continue;
				}

				$tax_query[] = [
					'taxonomy' => $taxonomy,
					'field'    => 'slug',
					'terms'    => array_keys( $slugs ),
					'operator' => 'IN',
				];
			}
		}

		if ( $tax_query ) {
			$args['tax_query'] = [
				'relation' => 'AND',
				...$tax_query,
			];
		}

		return new WP_Query( $args );
	}

	public function format_posts_to_html( array $posts ): string {
		$html = '';

		foreach ( $posts as $pst ) {
			$card_args = [
				Card_Controller::POST_ID           => $pst->ID,
				Card_Controller::STYLE             => Card_Controller::STYLE_PLAIN,
				Card_Controller::USE_TARGET_LINK   => false,
				Card_Controller::TITLE             => defer_template_part(
					'components/link/link',
					null,
					[
						Link_Controller::URL     => get_the_permalink( $pst ),
						Link_Controller::CONTENT => $pst->post_title,
					]
				),
				Card_Controller::DESCRIPTION       => defer_template_part(
					'components/text/text',
					null,
					[
						Text_Controller::CONTENT => wp_trim_words( get_the_excerpt( $pst ), self::WORDS_LIMIT ),
						Text_Controller::TAG     => 'p',
					],
				),
				Card_Controller::HIDE_MEMBERS_ONLY => false,
				Card_Controller::HIDE_TOPIC        => false,
				Card_Controller::TOP_MEMBERS_ONLY  => true,
			];

			if ( $main_category = $this->get_post_main_category( $pst->ID, $this->query_taxonomies ) ) {
				$card_args[ Card_Controller::MAIN_CATEGORY ] = defer_template_part(
					'components/link/link',
					null,
					[
						Link_Controller::CONTENT => $main_category->name,
						Link_Controller::URL     => get_category_link( $main_category ),
					]
				);
			}

			if ( ! $thumbnail = get_post_thumbnail_id( $pst ) ) {
				$thumbnail = get_field( Theme_Settings::DEFAULT_FEATURED_IMAGE, 'options' );
			}

			if ( $thumbnail ) {
				$card_args[ Card_Controller::IMAGE ] = defer_template_part(
					'components/image/image',
					null,
					[
						Image_Controller::IMG_ID       => $thumbnail,
						Image_Controller::USE_LAZYLOAD => true,
						Image_Controller::CLASSES      => [],
						Image_Controller::IMG_CLASSES  => [],
						Image_Controller::SRC_SIZE     => Image_Sizes::SIXTEEN_NINE,
						Image_Controller::LINK_URL     => get_the_permalink( $pst ),
					]
				);
			}

			$html .= defer_template_part( 'components/card/card', null, $card_args );
		}

		return $html;
	}

	public function get_query_post_types(): array {
		return $this->query_post_types;
	}

	public function get_query_taxonomies(): array {
		return $this->query_taxonomies;
	}

	/**
	 * @return null|string
	 */
	public function get_no_settings_error(): ?string {
		return Blocks_Utilities::format_admin_error_message( 'Select at least one post type and one taxonomy to show the block.' );
	}

	public function get_filters(): string {
		$filters = '';

		global $wp_taxonomies;

		foreach ( $this->query_taxonomies as $taxonomy ) {
			if ( $content_type_options = $this->get_filter_options( $taxonomy ) ) {
				$filters .= defer_template_part( 'components/text/text', null, [
					Text_Controller::CLASSES => [ 'b-advanced-content-filter__filter-name' ],
					Text_Controller::CONTENT => $wp_taxonomies[ $taxonomy ]->label,
					Text_Controller::TAG     => 'h5',
				] );

				$filters .= $this->generate_filter_container( $taxonomy, false, $content_type_options );
			}
		}

		return $filters;
	}

	public function get_archive_post_loader_attrs(): array {
		if ( ! is_archive() ) {
			return [];
		}

		$attrs = [
			'data-post-type' => get_post_type(),
		];

		$term = get_queried_object();

		if ( $term ) {
			$attrs[ 'data-taxonomy' ] = $term->taxonomy;
			$attrs[ 'data-term' ] = $term->slug;
		}

		return $attrs;
	}
}
