<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_use_cases;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Object_Meta\PPL_Options_Meta;
use Tribe\Project\Object_Meta\PPL_Settings;
use Tribe\Project\Templates\Models\ssl_Provider;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\slider\Slider_Controller;
use Tribe\Project\Blocks\Types\Listicle\ssl_XvsY_Use_Cases\ssl_XvsY_Use_Cases;

class ssl_XvsY_Use_Cases_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES = 'container_classes';
	public const CLASSES           = 'classes';
	public const ATTRS             = 'attrs';
	public const ROOT_CLASS        = 'b-ssl-xvsy-use-cases';
	public const INTRODUCTION_TEXT = 'intro_text';
	public const PRIMARY_CTA       = 'primary_cta';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;
	private string $primary_cta;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
		$this->primary_cta       = (string) $args[ self::PRIMARY_CTA ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
			self::PRIMARY_CTA       => '',
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Best Use Cases', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @param ssl_Provider $provider
	 * @param int          $index
	 *
	 * @return array
	 */
	private function get_provider_screenshot_sliders( ssl_Provider $provider, int $index ): array {
		$screenshots = [];

		$main_screenshot   = 0 === $index ? $this->get_provider_a_main_screenshot() : $this->get_provider_b_main_screenshot();
		$other_screenshots = $provider->get_screenshots();
		$screenshot_class  = self::ROOT_CLASS . '__screenshot';

		if ( $main_screenshot && $main_screenshot->get_url() ) {
			$screenshots[] = defer_template_part( 'components/image/image', null, [
				Image_Controller::IMG_URL      => esc_url( $main_screenshot->get_url() ),
				Image_Controller::AS_BG        => false,
				Image_Controller::USE_LAZYLOAD => true,
				Image_Controller::CLASSES      => [ $screenshot_class ],
				Image_Controller::IMG_ALT_TEXT => esc_html( $main_screenshot->get_caption() ),
				Image_Controller::HTML         => defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'figcaption',
					Text_Controller::CONTENT => esc_html( $main_screenshot->get_caption() ),
				] ),
			] );
		}

		if ( $other_screenshots ) {
			foreach ( $other_screenshots as $screenshot ) {
				if ( ! $screenshot->get_url() ) {
					continue;
				}

				$screenshots[] = defer_template_part( 'components/image/image', null, [
					Image_Controller::IMG_URL      => $screenshot->get_url(),
					Image_Controller::AS_BG        => false,
					Image_Controller::USE_LAZYLOAD => true,
					Image_Controller::CLASSES      => [ $screenshot_class ],
					Image_Controller::IMG_ALT_TEXT => $screenshot->get_caption(),
					Image_Controller::HTML         => defer_template_part( 'components/text/text', null, [
						Text_Controller::TAG     => 'figcaption',
						Text_Controller::CONTENT => $screenshot->get_caption(),
					] ),
				] );
			}
		}

		return $screenshots;
	}

	/**
	 * @return string
	 */
	protected function get_slider_options(): string {
		$args = [
			'slidesPerView' => 1,
			'breakpoints'   => [
				'1200' => [
					'pagination' => true,
				],
			],
		];

		return json_encode( $args );
	}

	/**
	 * @param string $provider_letter
	 *
	 * @return Deferred_Component
	 */
	private function get_provider_use_cases( string $provider_letter ): Deferred_Component {
		$list_content = '';

		$pub_review = $provider_letter === 'a' ? $this->get_provider_a_pub_review() : $this->get_provider_b_pub_review();

		if ( $pub_review && $pub_review->get_good_fit_use_cases() ) {
			foreach ( $pub_review->get_good_fit_use_cases() as $use_case ) {
				$title = defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'b',
					Text_Controller::CONTENT => esc_html( $use_case->get_title() ),
				] );

				$list_content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__list__item' ],
					Container_Controller::TAG     => 'li',
					Container_Controller::CONTENT => $title . ' ' . esc_html( $use_case->get_description() ),
				] );
			}
		}

		return defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__list' ],
			Container_Controller::TAG     => 'ol',
			Container_Controller::CONTENT => $list_content,
		] );
	}

	/**
	 * @return string
	 */
	public function get_accordion_content(): string {
		$content = '';

		if ( $intro = $this->introduction_text ) {
			$content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => esc_html( $intro ),
			] );
		}

		$comparison_content = '';

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {

			$provider_data = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h4',
				Text_Controller::CONTENT => esc_html( $provider->get_name() ),
			] );

			$main_attrs                        = [];
			$main_attrs['data-swiper-options'] = $this->get_slider_options();

			if ( $sliders = $this->get_provider_screenshot_sliders( $provider, $index ) ) {
				$show_navigation = count( $sliders ) > 1;

				$provider_data .= defer_template_part( 'components/slider/slider', null, [
					Slider_Controller::SHOW_ARROWS     => $show_navigation,
					Slider_Controller::SHOW_PAGINATION => $show_navigation,
					Slider_Controller::SLIDES          => $sliders,
					Slider_Controller::MAIN_ATTRS      => $main_attrs,
					Slider_Controller::CLASSES         => [ self::ROOT_CLASS . '__slider' ],
					Slider_Controller::SLIDE_CLASSES   => [ self::ROOT_CLASS . '__slider__item' ],
				] );
			}

			$provider_data .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__use-cases' ],
				Container_Controller::CONTENT => $this->get_provider_use_cases( $index === 0 ? 'a' : 'b' ),
			] );

			$comparison_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__column' ],
				Container_Controller::CONTENT => $provider_data,
			] );
		}

		$content .= defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__comparison' ],
			Container_Controller::CONTENT => $comparison_content,
		] );

		$ppl_btn = $this->get_primary_cta();

		if ( $ppl_btn ) {
			$custom_quote_text = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => 'Get free help from our project management software advisors to find your match.',
			] );

			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__quote' ],
				Container_Controller::CONTENT => defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__quote__container' ],
					Container_Controller::CONTENT => $custom_quote_text . $ppl_btn,
				] ),
			] );
		}

		return $content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return 'Best Use Cases For ' . $this->get_provider_a()->get_name() . ' And ' . $this->get_provider_b()->get_name();
	}

	/**
	 * @return Deferred_Component|null
	 */
	public function get_primary_cta(): ?Deferred_Component {
		$primary_cta = $this->primary_cta;
		$taxonomy_id = null;
		$classes     = [];

		$tool_name_a = sanitize_title( $this->get_provider_a()->get_name() );
		$tool_name_b = sanitize_title( $this->get_provider_b()->get_name() );

		if ( ! empty( $ppl_option_category_id = (string) get_field( PPL_Options_Meta::CATEGORY_ID, get_the_ID() ) ) ) {
			$taxonomy_id = $ppl_option_category_id;
		} else {
			$taxonomy_id = (string) PPL_Settings::get_field_by_post_type( get_post_type(), PPL_Settings::NAME, PPL_Settings::PPL_CATEGORY_ID_DEFAULT, 'option' );
		}

		if ( empty( $taxonomy_id ) ) {
			return null;
		}

		$classes = [
			'item-single__cta-link',
			'a-btn',
		];

		$ppl_url = '#modal-id-ppl-form||category-id-' . $taxonomy_id . '||post-id-' . get_the_ID();

		if ( $primary_cta === ssl_XvsY_Use_Cases::CTA_OPTION_GET_CUSTOM_QUOTE_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Custom Quote', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );
		} elseif ( $primary_cta === ssl_XvsY_Use_Cases::CTA_OPTION_GET_EXPERT_ADVICE_NO_UTM ) {
			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Get Expert Advice', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} elseif ( $primary_cta === ssl_XvsY_Use_Cases::CTA_OPTION_BOOK_DEMO_UTM ) {
			$ppl_url .= '||utm-provider-' . $tool_name_a . '--provider-' . $tool_name_b;

			return defer_template_part( 'components/link/link', null, [
				Link_Controller::URL     => $ppl_url,
				Link_Controller::CONTENT => __( 'Book Demo', 'tribe' ),
				Link_Controller::TARGET  => '_blank',
				Link_Controller::CLASSES => $classes,
			] );

		} else {
			return null;
		}
	}

}
