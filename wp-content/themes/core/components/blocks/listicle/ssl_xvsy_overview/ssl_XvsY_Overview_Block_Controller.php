<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\blocks\listicle\ssl_xvsy_overview;

use Tribe\Libs\Utils\Markup_Utils;
use Tribe\Project\Templates\Models\ssl_Provider;
use Tribe\Project\Templates\Models\ssl_Analyst_Review_Score;
use Tribe\Project\Templates\Components\ssl_XvsY_Controller;
use Tribe\Project\Templates\Components\image\Image_Controller;
use Tribe\Project\Templates\Components\link\Link_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\score\Score_Controller;
use Tribe\Project\Templates\Components\container\Container_Controller;
use Tribe\Project\Templates\Components\slider\Slider_Controller;
use Tribe\Project\Templates\Components\rating\Rating_Controller;

class ssl_XvsY_Overview_Block_Controller extends ssl_XvsY_Controller {
	public const CONTAINER_CLASSES    = 'container_classes';
	public const CLASSES              = 'classes';
	public const ATTRS                = 'attrs';
	public const ROOT_CLASS           = 'b-ssl-xvsy-overview';
	public const INTRODUCTION_TEXT    = 'intro_text';
	public const DATALAYER_BLOCK_NAME = 'xvsy-overview';

	private array  $container_classes;
	private array  $classes;
	private array  $attrs;
	private string $introduction_text;

	/**
	 * @param array $args
	 */
	public function __construct( array $args = [] ) {
		parent::__construct( $args );

		$args = $this->parse_args( $args );

		$this->container_classes = (array) $args[ self::CONTAINER_CLASSES ];
		$this->classes           = (array) $args[ self::CLASSES ];
		$this->attrs             = (array) $args[ self::ATTRS ];
		$this->introduction_text = (string) $args[ self::INTRODUCTION_TEXT ];
	}

	/**
	 * @return array
	 */
	protected function defaults(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [],
			self::ATTRS             => [],
		];
	}

	/**
	 * @return array
	 */
	protected function required(): array {
		return [
			self::CONTAINER_CLASSES => [],
			self::CLASSES           => [ 'c-block', self::ROOT_CLASS ],
		];
	}

	/**
	 * @return string
	 */
	public function get_classes(): string {
		return Markup_Utils::class_attribute( $this->classes );
	}

	/**
	 * @return string
	 */
	public function get_attrs(): string {
		if ( is_admin() ) {
			$this->attrs['data-toc-title'] = __( 'Overview', 'tribe' );
		}

		return Markup_Utils::concat_attrs( $this->attrs );
	}

	/**
	 * @return string
	 */
	public function get_container_classes(): string {
		return Markup_Utils::class_attribute( $this->container_classes );
	}

	/**
	 * @param ssl_Provider $provider
	 * @param int          $index
	 *
	 * @return array
	 */
	private function get_provider_screenshot_sliders( ssl_Provider $provider, int $index ): array {
		$screenshots = [];

		$main_screenshot   = 0 === $index ? $this->get_provider_a_main_screenshot() : $this->get_provider_b_main_screenshot();
		$other_screenshots = $provider->get_screenshots();
		$screenshot_class  = self::ROOT_CLASS . '__screenshot';

		if ( $main_screenshot && $main_screenshot->get_url() ) {
			$screenshots[] = defer_template_part( 'components/image/image', null, [
				Image_Controller::IMG_URL      => esc_url( $main_screenshot->get_url() ),
				Image_Controller::AS_BG        => false,
				Image_Controller::USE_LAZYLOAD => true,
				Image_Controller::CLASSES      => [ $screenshot_class ],
				Image_Controller::IMG_ALT_TEXT => esc_html( $main_screenshot->get_caption() ),
				Image_Controller::HTML         => defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'figcaption',
					Text_Controller::CONTENT => esc_html( $main_screenshot->get_caption() ),
				] ),
			] );
		}

		if ( $other_screenshots ) {
			foreach ( $other_screenshots as $screenshot ) {
				if ( ! $screenshot->get_url() ) {
					continue;
				}

				$screenshots[] = defer_template_part( 'components/image/image', null, [
					Image_Controller::IMG_URL      => esc_url( $screenshot->get_url() ),
					Image_Controller::AS_BG        => false,
					Image_Controller::USE_LAZYLOAD => true,
					Image_Controller::CLASSES      => [ $screenshot_class ],
					Image_Controller::IMG_ALT_TEXT => esc_html( $screenshot->get_caption() ),
					Image_Controller::HTML         => defer_template_part( 'components/text/text', null, [
						Text_Controller::TAG     => 'figcaption',
						Text_Controller::CONTENT => esc_html( $screenshot->get_caption() ),
					] ),
				] );
			}
		}

		return $screenshots;
	}

	/**
	 * @param ssl_Provider $provider
	 *
	 * @return string
	 */
	private function get_provider_rating_content( ssl_Provider $provider ): string {
		$content           = '';
		$reviews_content   = '';
		$crozscore_content = '';
		$reviews           = $provider->get_analyst_review();
		$crozscore         = $provider->get_crozscore();

		if ( $reviews ) {
			$review_score_sum = array_sum( array_map( function ( ssl_Analyst_Review_Score $score ) {
				return $score->get_score();
			}, $reviews ) );

			if ( $review_score_sum > 0 ) {
				$rating_title = defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'span',
					Text_Controller::CLASSES => [ 't-overline' ],
					Text_Controller::CONTENT => __( 'OUR RATING', 'tribe' ),
				] );

				$review_score_avg = ( $review_score_sum / ( 5 * count( $reviews ) ) ) * 100;

				$rating_score = defer_template_part( 'components/score/score', null, [
					Score_Controller::SCORE_CURRENT => $review_score_avg,
					Score_Controller::SCORE_MAX     => 100,
				] );

				$reviews_content = defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__rating__score' ],
					Container_Controller::CONTENT => $rating_title . $rating_score,
				] );
			}
		}

		if ( $crozscore ) {
			$rating_title = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CLASSES => [ 't-overline' ],
				Text_Controller::CONTENT => __( 'CROZSCORE', 'tribe' ),
			] );

			$round_score = floatval( preg_replace( '/[^\d.]/', '', number_format( $crozscore, 1 ) ) );

			$rating_score = defer_template_part( 'components/score/score', null, [
				Score_Controller::SCORE_CURRENT => $round_score,
				Score_Controller::SCORE_MAX     => 5,
			] );

			$crozscore_content = defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__rating__score' ],
				Container_Controller::CONTENT => $rating_title . $rating_score,
			] );
		}

		if ( $reviews_content && $crozscore_content ) {
			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__rating__container' ],
				Container_Controller::CONTENT => $reviews_content . $crozscore_content,
			] );
		} elseif ( $reviews_content ) {
			$content .= $reviews_content;
		} elseif ( $crozscore_content ) {
			$content .= $crozscore_content;
		}

		if ( $provider->get_trial_info() || $provider->get_price_info() ) {
			$pricing_title = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'span',
				Text_Controller::CLASSES => [ 't-overline' ],
				Text_Controller::CONTENT => __( 'PRICING', 'tribe' ),
			] );

			$price_info_items = '';

			if ( $provider->get_trial_info() ) {
				$price_info_items .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CONTENT => esc_html( $provider->get_trial_info() ),
					Container_Controller::TAG     => 'li',
				] );
			}

			if ( $provider->get_price_info() ) {
				$price_info_items .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CONTENT => esc_html( $provider->get_price_info() ),
					Container_Controller::TAG     => 'li',
				] );
			}

			$pricing_info_list = defer_template_part( 'components/container/container', null, [
				Container_Controller::CONTENT => $price_info_items,
				Container_Controller::TAG     => 'ul',
			] );

			$content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__rating__price' ],
				Container_Controller::CONTENT => $pricing_title . $pricing_info_list,
			] );
		}

		return $content;
	}

	/**
	 * @param ssl_Provider $provider
	 *
	 * @return string|null
	 */
	private function get_provider_score_summary_content( ssl_Provider $provider ): ?string {
		$content = '';
		$reviews = $provider->get_analyst_review();

		if ( $reviews ) {
			$scores = '';

			foreach ( $reviews as $review ) {
				if ( ! $review->get_score() ) {
					continue;
				}

				$score = defer_template_part( 'components/rating/rating', null, [
					Rating_Controller::SCORE => $review->get_score(),
				] );

				$score .= defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'span',
					Text_Controller::CONTENT => esc_html( $review->get_analyst_review_category() ),
				] );

				$scores .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__score-summary__scores__score' ],
					Container_Controller::CONTENT => $score,
				] );
			}

			if ( $scores ) {
				$content .= defer_template_part( 'components/text/text', null, [
					Text_Controller::TAG     => 'span',
					Text_Controller::CLASSES => [ 't-overline' ],
					Text_Controller::CONTENT => __( 'SCORE SUMMARY', 'tribe' ),
				] );

				$content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__score-summary__scores' ],
					Container_Controller::CONTENT => $scores,
				] );
			}
		}

		return $content ?: null;
	}

	/**
	 * @return string
	 */
	protected function get_slider_options(): string {
		$args = [
			'slidesPerView' => 1,
		];

		return json_encode( $args );
	}

	/**
	 * @return string
	 */
	public function get_accordion_content(): string {
		$content = '';

		if ( is_admin() ) {
			$content = $this->get_logo_images() ?: '';
		}

		if ( $intro = $this->introduction_text ) {
			$content .= defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'p',
				Text_Controller::CONTENT => $intro,
			] );
		}

		$comparison_content = '';

		foreach ( [ $this->get_provider_a(), $this->get_provider_b() ] as $index => $provider ) {

			$provider_content = defer_template_part( 'components/text/text', null, [
				Text_Controller::TAG     => 'h4',
				Text_Controller::CONTENT => $provider->get_name(),
			] );

			$main_attrs                        = [];
			$main_attrs['data-swiper-options'] = $this->get_slider_options();

			if ( $sliders = $this->get_provider_screenshot_sliders( $provider, $index ) ) {
				$show_navigation = count( $sliders ) > 1;

				$provider_content .= defer_template_part( 'components/slider/slider', null, [
					Slider_Controller::SHOW_ARROWS     => $show_navigation,
					Slider_Controller::SHOW_PAGINATION => $show_navigation,
					Slider_Controller::SLIDES          => $sliders,
					Slider_Controller::MAIN_ATTRS      => $main_attrs,
					Slider_Controller::CLASSES         => [ self::ROOT_CLASS . '__slider' ],
					Slider_Controller::SLIDE_CLASSES   => [ self::ROOT_CLASS . '__slider__item' ],
				] );
			}

			$provider_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__rating' ],
				Container_Controller::CONTENT => $this->get_provider_rating_content( $provider ),
			] );

			if ( $score_content = $this->get_provider_score_summary_content( $provider ) ) {
				$provider_content .= defer_template_part( 'components/container/container', null, [
					Container_Controller::CLASSES => [ self::ROOT_CLASS . '__score-summary' ],
					Container_Controller::CONTENT => $score_content,
				] );
			}

			$page_url = 0 === $index ? $this->get_provider_a_pub_review()->get_page_url() : $this->get_provider_b_pub_review()->get_page_url();

			if ( $page_url ) {
				$provider_content .= defer_template_part( 'components/link/link', null, [
					Link_Controller::URL     => $page_url,
					Link_Controller::CONTENT => 'Read ' . $provider->get_name() . ' Review',
					Link_Controller::TARGET  => '_blank',
					Link_Controller::CLASSES => [ 'a-btn' ],
					Link_Controller::ATTRS   => 0 === $index ? $this->get_provider_a_review_link_attrs( self::DATALAYER_BLOCK_NAME ) : $this->get_provider_b_review_link_attrs( self::DATALAYER_BLOCK_NAME ),
				] );
			}

			$comparison_content .= defer_template_part( 'components/container/container', null, [
				Container_Controller::CLASSES => [ self::ROOT_CLASS . '__column' ],
				Container_Controller::CONTENT => $provider_content,
			] );
		}

		$content .= defer_template_part( 'components/container/container', null, [
			Container_Controller::CLASSES => [ self::ROOT_CLASS . '__comparison' ],
			Container_Controller::CONTENT => $comparison_content,
		] );

		return $content;
	}

	/**
	 * @return string
	 */
	public function get_accordion_title(): string {
		return $this->get_provider_a()->get_name() . ' Vs. ' . $this->get_provider_b()->get_name() . ': An Overview';
	}

	private function get_logo_images(): ?string {
		if ( ! $this->get_provider_a()->get_logo_url() && ! $this->get_provider_b()->get_logo_url() ) {
			return null;
		}

		$logo_provider_a = defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => $this->get_provider_a()->get_logo_url(),
			Image_Controller::AS_BG        => false,
			Image_Controller::SRC_SIZE     => 'medium_large',
			Image_Controller::SRCSET_SIZES => [
				'medium',
				'medium_large',
			],
			Image_Controller::CLASSES      => [
				self::ROOT_CLASS . '__logo',
			],
		] );

		$logo_provider_b = defer_template_part( 'components/image/image', null, [
			Image_Controller::IMG_URL      => $this->get_provider_b()->get_logo_url(),
			Image_Controller::AS_BG        => false,
			Image_Controller::SRC_SIZE     => 'medium_large',
			Image_Controller::SRCSET_SIZES => [
				'medium',
				'medium_large',
			],
			Image_Controller::CLASSES      => [
				self::ROOT_CLASS . '__logo',
			],
		] );

		return $logo_provider_a . $logo_provider_b;
	}
}
