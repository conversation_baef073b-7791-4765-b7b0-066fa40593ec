/* -----------------------------------------------------------------------------
 *
 * Scrollable Content
 *
 * ----------------------------------------------------------------------------- */

.b-scrollable-content {
	padding: var(--spacer-30) var(--spacer-20) var(--spacer-30) var(--spacer-30);
	border: 1px solid var(--color-neutral-20);
	margin-bottom: var(--spacer-30);

	&__container {
		@mixin scrollbar-neutral;
		width: 100%;
		height: auto;
		max-height: 50vh;
		overflow-y: auto;

		> * {
			margin-bottom: var(--spacer-20);

			&:last-child {
				margin-bottom: 0;
			}
		}

		.acf-innerblocks-container {
			p,
			table {
				&:not(:last-child) {
					margin-bottom: var(--spacer-40);
				}
			}

			.table-prevent-word-breaking {
				table {
					.t-sink & {
						max-width: calc(var(--grid-width) - var(--grid-sidebar-col) - var(--spacer-80) - var(--spacer-30) - var(--spacer-20) - 10px);
					}
				}
			}
		}
	}
}
