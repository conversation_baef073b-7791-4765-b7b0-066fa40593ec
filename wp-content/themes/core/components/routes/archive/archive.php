<?php
declare( strict_types=1 );

use Tribe\Project\Templates\Components\routes\archive\Archive_Controller;

$c = Archive_Controller::factory();
$c->render_header();

?>
<main id="main-content">
	<?php

	$term = get_queried_object();

	if ( $term && ! is_wp_error( $term ) && ! empty( $term->taxonomy ) ) {
		echo $c->get_advanced_content_filter_block( get_post_type(), $term->taxonomy, $term->slug );
	} else {
		echo $c->get_advanced_content_filter_block( get_post_type() );
	}

	?>
</main>
<?php

$c->render_footer();
