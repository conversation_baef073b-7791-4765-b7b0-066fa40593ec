<?php
declare( strict_types=1 );

namespace Tribe\Project\Templates\Components\routes\archive;

use Tribe\Project\Post_Types\Media\Media;
use Tribe\Project\Post_Types\Member_Stream\Member_Stream;
use Tribe\Project\Taxonomies\Category\Category;
use Tribe\Project\Taxonomies\Event_Type\Event_Type;
use Tribe\Project\Taxonomies\Media_Type\Media_Type;
use Tribe\Project\Templates\Components\Abstract_Controller;
use Tribe\Project\Templates\Components\blocks\advanced_content_filter\Advanced_Content_Filter_Block_Controller;
use Tribe\Project\Templates\Components\Deferred_Component;
use Tribe\Project\Templates\Components\sidebar\Sidebar_Controller;
use Tribe\Project\Templates\Components\text\Text_Controller;
use Tribe\Project\Templates\Components\Traits\Page_Title;

class Archive_Controller extends Abstract_Controller {
	use Page_Title;

	/**
	 * @var int|string
	 */
	private $sidebar_id = '';

	/**
	 * Render the header component
	 *
	 * Bypasses the get_header() function to
	 * load our component instead of header.php
	 *
	 * @return void
	 */
	public function render_header(): void {
		do_action( 'get_header', null );
		get_template_part( 'components/document/header/header', 'index' );
	}


	/**
	 * Render the sidebar component
	 *
	 * Bypasses the get_sidebar() function to
	 * load our component instead of sidebar.php
	 *
	 * @return void
	 */
	public function render_sidebar(): void {
		do_action( 'get_sidebar', null );
		get_template_part(
			'components/sidebar/sidebar',
			'index',
			[ Sidebar_Controller::SIDEBAR_ID => $this->sidebar_id ]
		);
	}

	/**
	 * Render the footer component
	 *
	 * Bypasses the get_footer() function to
	 * load our component instead of footer.php
	 *
	 * @return void
	 */
	public function render_footer(): void {
		do_action( 'get_footer', null );
		get_template_part( 'components/document/footer/footer', 'index' );
	}

	public function get_pagination(): string {
		global $wp_query;

		$page = get_query_var( 'paged' ) ?: 1;

		if ( 0 === $wp_query->post_count ) {
			$page = 0;
		}

		return sprintf(
			/* translators: 1: current page 2: total pages */
			__( 'Page %s of %s', 'tribe' ),
			$page,
			$wp_query->max_num_pages
		);
	}

	public function get_title_args(): array {
		if ( empty( $this->get_page_title() ) ) {
			return [];
		}

		return [
			Text_Controller::TAG     => 'h1',
			Text_Controller::CLASSES => [ 'page-title', 'h1' ],
			Text_Controller::CONTENT => $this->get_page_title(),
		];
	}

	public function get_advanced_content_filter_block( string $post_type, string $taxonomy = '', string $term = '' ): Deferred_Component {
		return defer_template_part( 'components/blocks/advanced_content_filter/advanced_content_filter', null, [
			Advanced_Content_Filter_Block_Controller::BLOCK_ID         => 'archive',
			Advanced_Content_Filter_Block_Controller::ATTRS            => [],
			Advanced_Content_Filter_Block_Controller::CLASSES          => [],
			Advanced_Content_Filter_Block_Controller::TITLE            => $this->get_page_title(),
			Advanced_Content_Filter_Block_Controller::HEADING_TAG      => 'h1',
			Advanced_Content_Filter_Block_Controller::QUERY_POST_TYPES => [ $post_type ],
			Advanced_Content_Filter_Block_Controller::ARCHIVE_TAXONOMY => $taxonomy,
			Advanced_Content_Filter_Block_Controller::ARCHIVE_TERM     => $term,
			Advanced_Content_Filter_Block_Controller::QUERY_TAXONOMIES => $this->get_filterable_taxonomies( $post_type ),
		] );
	}

	public function get_filterable_taxonomies( string $post_type ): array {
		$taxonomies_slugs = [];

		if ( $post_type === Media::NAME ) {
			$taxonomies_slugs = [ Media_Type::NAME ];
		} elseif ( $post_type === Member_Stream::NAME ) {
			$taxonomies_slugs = [ Event_Type::NAME ];
		}

		$taxonomies_slugs[] = Category::NAME;

		return $taxonomies_slugs;
	}
}
