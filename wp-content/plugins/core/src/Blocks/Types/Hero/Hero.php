<?php
declare( strict_types=1 );

namespace Tribe\Project\Blocks\Types\Hero;

use Tribe\Libs\ACF\Field_Section;
use Tribe\Libs\ACF\Field;
use Tribe\Project\Blocks\Lib\Block_Config_Json;
use Tribe\Project\Templates\Components\Traits\With_Lottie_Fields;

class Hero extends Block_Config_Json {
	use With_Lottie_Fields;

	public const NAME = 'hero';

	public const SECTION_CONTENT = 's-content';
	public const IMAGE           = 'image';
	public const BG_IMAGE        = 'background_image';
	public const VIDEO           = 'video';
	public const HTML            = 'html';
	public const IMAGE_LEFT      = 'image_left';
	public const IMAGE_RIGHT     = 'image_right';

	public const TITLE                = 'title';
	public const DESCRIPTION          = 'description';
	public const CTA                  = 'cta';
	public const CTA_ID               = 'cta_id';
	public const HEADING_TAG          = 'heading_tag';
	public const HEADING_TAG_H1       = 'h1';
	public const HEADING_TAG_H2       = 'h2';
	public const HEADING_TAG_H3       = 'h3';
	public const HEADING_TAG_H4       = 'h4';
	public const SECONDARY_CTA        = 'secondary_cta';
	public const SECONDARY_CTA_LEADIN = 'secondary_cta_leadin';
	public const IMAGE_BEFORE_LEADIN  = 'image_before_leadin';

	public const SECTION_SETTINGS             = 's-settings';
	public const MEDIA_TYPE                   = 'media_type';
	public const MEDIA_TYPE_IMAGE             = 'media_type_image';
	public const MEDIA_TYPE_BACKGROUND_IMAGE  = 'media_type_background_image';
	public const MEDIA_TYPE_VIDEO             = 'media_type_video';
	public const MEDIA_TYPE_HTML              = 'media_type_html';
	public const MEDIA_TYPE_LOTTIE            = 'media_type_lottie';
	public const CUSTOM_CLASSES_PRIMARY_CTA   = 'custom_classes_primary_cta';
	public const CUSTOM_CLASSES_SECONDARY_CTA = 'custom_classes_secondary_cta';

	/**
	 * Register the block
	 */
	public function add_block(): void {
		$this->set_block_json( self::NAME, [
			Block_Config_Json::LOAD_STYLE => true,
		] );
	}

	/**
	 * Register Fields for block
	 */
	public function add_fields(): void {
		//==========================================
		// Content Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_CONTENT, __( 'Content', 'tribe' ), 'accordion' )
		)->add_field( new Field( self::NAME . '_' . self::IMAGE, [
				'label'             => __( 'Featured Image', 'tribe' ),
				'name'              => self::IMAGE,
				'type'              => 'image',
				'return_format'     => 'id',
				'conditional_logic' => [
					[
						'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
						'operator' => '==',
						'value'    => self::MEDIA_TYPE_IMAGE,
					],
				],
			] )
		)->add_field( new Field( self::NAME . '_' . self::BG_IMAGE, [
				'label'             => __( 'Background Image', 'tribe' ),
				'name'              => self::BG_IMAGE,
				'type'              => 'image',
				'return_format'     => 'id',
				'conditional_logic' => [
					[
						'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
						'operator' => '==',
						'value'    => self::MEDIA_TYPE_BACKGROUND_IMAGE,
					],
				],
			] )
		)->add_field( new Field( self::NAME . '_' . self::VIDEO, [
				'label'             => __( 'Video', 'tribe' ),
				'name'              => self::VIDEO,
				'type'              => 'oembed',
				'conditional_logic' => [
					[
						'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
						'operator' => '==',
						'value'    => self::MEDIA_TYPE_VIDEO,
					],
				],
			] )
		)->add_field( new Field( self::NAME . '_' . self::HTML, [
				'label'             => __( 'Embed a video or podcast with an iframe.', 'tribe' ),
				'name'              => self::HTML,
				'type'              => 'textarea',
				'conditional_logic' => [
					[
						'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
						'operator' => '==',
						'value'    => self::MEDIA_TYPE_HTML,
					],
				],
			] )
		)->add_field( new Field( self::NAME . '_' . self::IMAGE_LEFT, [
				'label'             => __( 'Image Left', 'tribe' ),
				'name'              => self::IMAGE_LEFT,
				'type'              => 'image',
				'return_format'     => 'id',
				'conditional_logic' => [
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::MEDIA_TYPE_VIDEO,
						],
					],
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::MEDIA_TYPE_HTML,
						],
					],
				],
			] )
		)->add_field( new Field( self::NAME . '_' . self::IMAGE_RIGHT, [
				'label'             => __( 'Image Right', 'tribe' ),
				'name'              => self::IMAGE_RIGHT,
				'type'              => 'image',
				'return_format'     => 'id',
				'conditional_logic' => [
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::MEDIA_TYPE_VIDEO,
						],
					],
					[
						[
							'field'    => 'field_' . self::NAME . '_' . self::MEDIA_TYPE,
							'operator' => '==',
							'value'    => self::MEDIA_TYPE_HTML,
						],
					],
				],
			] )
		)->add_field(
			$this->get_lottie_fields( self::NAME . '_' . self::MEDIA_TYPE, self::MEDIA_TYPE_LOTTIE )
		)->add_field( new Field( self::NAME . '_' . self::TITLE, [
				'label' 	   => __( 'Title', 'tribe' ),
				'name'  	   => self::TITLE,
				'type'  	   => 'text',
				'instructions' => __( 'Microinteraction: You can highlight <i>up to 5</i> rotating words in the title using the special <code>{{rotate}}</code> tag. Example: <code>{{rotate:first word|second word|third word}}</code>. (Only available for pages with GSAP.)', 'tribe' ),
			] )
		)->add_field( new Field( self::NAME . '_' . self::DESCRIPTION, [
				'label'        => __( 'Description', 'tribe' ),
				'name'         => self::DESCRIPTION,
				'type'         => 'wysiwyg',
				'toolbar'      => 'basic',
				'media_upload' => 0,
			] )
		)->add_field( new Field( self::NAME . '_' . self::CTA, [
				'label' => __( 'Call to Action', 'tribe' ),
				'name'  => self::CTA,
				'type'  => 'link',
			] )
		)->add_field( new Field( self::NAME . '_' . self::CTA_ID, [
				'label' => __( 'CTA Button ID', 'tribe' ),
				'name'  => self::CTA_ID,
				'type'  => 'text',
			] )
		)->add_field( new Field( self::NAME . '_' . self::CUSTOM_CLASSES_PRIMARY_CTA, [
				'label' => __( 'Custom CSS classes for primary cta', 'tribe' ),
				'name'  => self::CUSTOM_CLASSES_PRIMARY_CTA,
				'type'  => 'text',
			] )
		)->add_field( new Field( self::NAME . '_' . self::HEADING_TAG, [
				'label'         => __( 'Heading Tag', 'tribe' ),
				'name'          => self::HEADING_TAG,
				'type'          => 'select',
				'ui'            => 1,
				'multiple'      => 0,
				'allow_null'    => 0,
				'choices'       => [
					self::HEADING_TAG_H1 => self::HEADING_TAG_H1,
					self::HEADING_TAG_H2 => self::HEADING_TAG_H2,
					self::HEADING_TAG_H3 => self::HEADING_TAG_H3,
					self::HEADING_TAG_H4 => self::HEADING_TAG_H4,
				],
				'default_value' => self::HEADING_TAG_H2,
			] )
		)->add_field( new Field( self::NAME . '_' . self::SECONDARY_CTA, [
				'label' => __( 'Secondary CTA', 'tribe' ),
				'name'  => self::SECONDARY_CTA,
				'type'  => 'link',
			] )
		)->add_field( new Field( self::NAME . '_' . self::CUSTOM_CLASSES_SECONDARY_CTA, [
				'label' => __( 'Custom CSS classes for secondary cta', 'tribe' ),
				'name'  => self::CUSTOM_CLASSES_SECONDARY_CTA,
				'type'  => 'text',
			] )
		)->add_field( new Field( self::NAME . '_' . self::SECONDARY_CTA_LEADIN, [
				'label' => __( 'Secondary CTA Lead-in', 'tribe' ),
				'name'  => self::SECONDARY_CTA_LEADIN,
				'type'  => 'text',
			] )
		)->add_field( new Field( self::NAME . '_' . self::IMAGE_BEFORE_LEADIN, [
				'label'         => __( 'Image Before Lead-in', 'tribe' ),
				'name'          => self::IMAGE_BEFORE_LEADIN,
				'type'          => 'image',
				'return_format' => 'id',
				'instructions'  => __( 'This image will appear on the left side of Secondary CTA & Lead-in. Image dimensions 200x45px.', 'tribe' ),
			] )
		);
		//==========================================
		// Setting Fields
		//==========================================
		$this->add_section( new Field_Section( self::SECTION_SETTINGS, __( 'Settings', 'tribe' ), 'accordion' )
		)->add_field( new Field( self::NAME . '_' . self::MEDIA_TYPE, [
				'label'         => __( 'Media Type', 'tribe' ),
				'name'          => self::MEDIA_TYPE,
				'type'          => 'radio',
				'choices'       => [
					self::MEDIA_TYPE_IMAGE            => __( 'Featured Image', 'tribe' ),
					self::MEDIA_TYPE_BACKGROUND_IMAGE => __( 'Background Image', 'tribe' ),
					self::MEDIA_TYPE_VIDEO            => __( 'Video', 'tribe' ),
					self::MEDIA_TYPE_HTML             => __( 'HTML', 'tribe' ),
					self::MEDIA_TYPE_LOTTIE           => __( 'Lottie Animation', 'tribe' ),
				],
				'default_value' => [
					self::MEDIA_TYPE_IMAGE,
				],
			] )
		);
	}

}
