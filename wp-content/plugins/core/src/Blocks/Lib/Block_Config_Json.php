<?php

namespace Tribe\Project\Blocks\Lib;

use Tribe\Libs\ACF\Block_Config;

abstract class Block_Config_Json extends Block_Config {

	const COMPONENT_FILE_NAME   = 'component_file_name';
	const BLOCK_VERSION         = 'block_version';
	const LOAD_STYLE            = 'load_style';
	const STYLE_HANDLE          = 'style_handle';
	const CUSTOM_STYLE_FILE_SRC = 'custom_style_file_src';
	const NORMALIZED_BLOCK_NAME = 'normalized_block_name';

	public function __construct() {
		parent::__construct();
	}

	public function set_block_json( $block_name, $args = [] ): void {
		$defaults = [
			self::COMPONENT_FILE_NAME   => $block_name,
			self::BLOCK_VERSION         => '1.0.0',
			self::LOAD_STYLE            => false,
			self::STYLE_HANDLE          => "style-block-$block_name",
			self::CUSTOM_STYLE_FILE_SRC => null,
			self::NORMALIZED_BLOCK_NAME => 'acf/' . str_replace( '_', '', $block_name ),
		];

		$args = wp_parse_args( $args, $defaults );

		if ( $args[ self::LOAD_STYLE ] ) {
			$style_file_src  = trailingslashit( get_template_directory_uri() ) . 'assets/css/dist/theme/blocks/' . $args[ self::COMPONENT_FILE_NAME ] . '/' . $args[ self::COMPONENT_FILE_NAME ] . '.min.css';
			$style_file_path = trailingslashit( get_template_directory() ) . 'assets/css/dist/theme/blocks/' . $args[ self::COMPONENT_FILE_NAME ] . '/' . $args[ self::COMPONENT_FILE_NAME ] . '.min.css';

			if ( $args[ self::CUSTOM_STYLE_FILE_SRC ] ) {
				$style_file_src = $args[ self::CUSTOM_STYLE_FILE_SRC ];
			}

			// TODO: Review the injection of blocks: https://airtable.com/app9IDPTZMP8ilIxo/tbl5vCOCRALfB1gOq/viwzyVC9fyxEAvgYp/recgrOVmKWgcKlBFB?blocks=hide
			$injection_blocks = [
				'ad_slot',
				'inline_newsletter',
				'inline_recommendations',
				'ppl_ad',
				'ppl_inline',
				'our_methodology',
				'manual_ad',
				'ssl_3_pack_ad',
				'interstitial',
				'content_loop',
				'blockquote_with_avatar',
				'video_gallery',
				'listicle',
				'table_of_contents',
				'links',
				'key_takeaways',
				'accordion_advanced',
				'simple_embed',
				'media_text',
				'advanced_content_filter',
			];

			if ( in_array( $args[ self::COMPONENT_FILE_NAME ], $injection_blocks ) ) {
				wp_enqueue_style( $args[ self::STYLE_HANDLE ], $style_file_src, [], $args[ self::BLOCK_VERSION ] );
			} else {
				wp_enqueue_block_style( $args[ self::NORMALIZED_BLOCK_NAME ], [
					'handle' => $args[ self::STYLE_HANDLE ],
					'src'    => $style_file_src,
					'path'   => $style_file_path,
					'ver'    => $args[ self::BLOCK_VERSION ],
				] );
			}
		}

		// Register the block JSON file
		register_block_type( trailingslashit( get_template_directory() ) . "blocks/$block_name/block.json" );
	}
}
