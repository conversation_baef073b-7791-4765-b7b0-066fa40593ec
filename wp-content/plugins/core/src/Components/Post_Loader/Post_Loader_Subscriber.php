<?php

namespace Tribe\Project\Components\Post_Loader;

use Tribe\Libs\Container\Abstract_Subscriber;

class Post_Loader_Subscriber extends Abstract_Subscriber {
	public function register(): void {
		add_action( 'wp_ajax_nopriv_post_loader', function () {
			$this->container->get( Post_Loader::class )->load_ajax_posts();
		} );

		add_action( 'wp_ajax_post_loader', function () {
			$this->container->get( Post_Loader::class )->load_ajax_posts();
		} );

		add_action( 'pre_get_posts', function ( $query ) {
			$this->container->get( Post_Loader::class )->modify_archive_query_to_match_advanced_content_filter_query( $query );
		} );
	}
}
